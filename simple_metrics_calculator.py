#!/usr/bin/env python3
"""
Simple validation metrics calculator for SML depth estimation results.
This script compares SML output PNG depth maps against ground truth.

Edit the paths below to match your data structure.
"""

import os
import numpy as np
from PIL import Image
import glob

# Import evaluation utilities
import sys
sys.path.append('.')
import utils.eval_utils as eval_utils


def load_depth_png(depth_path):
    """Load depth from PNG file (scaled by 256)"""
    depth = np.array(Image.open(depth_path), dtype=np.float32) / 256.0
    depth[depth <= 0] = 0.0
    return depth


def crop_to_zju_format(depth_image):
    """
    Crop depth image to ZJU dataset format if height is 720
    This matches the cropping done in data/datasets.py: [:, 720 // 3: 720 // 4 * 3]
    """
    if len(depth_image.shape) == 2:
        height, width = depth_image.shape
    else:
        height, width = depth_image.shape[:2]

    if height == 720:
        # Apply the same cropping as in datasets.py
        return depth_image[720 // 3: 720 // 4 * 3, :]  # [240:540, :]
    else:
        return depth_image


def ensure_same_dimensions(pred_depth, gt_depth):
    """
    Ensure predicted and ground truth depths have the same dimensions
    Handle the case where GT might be full size (720) but prediction is cropped (300)
    """
    pred_h, pred_w = pred_depth.shape[:2]
    gt_h, gt_w = gt_depth.shape[:2]

    # If GT is 720 high and prediction is 300 high, crop GT to match
    if gt_h == 720 and pred_h == 300:
        gt_depth = crop_to_zju_format(gt_depth)
        print(f"  Cropped GT from {gt_h}x{gt_w} to {gt_depth.shape[0]}x{gt_depth.shape[1]} to match prediction")

    # Check if dimensions match now
    if pred_depth.shape != gt_depth.shape:
        print(f"  Warning: Dimension mismatch - Pred: {pred_depth.shape}, GT: {gt_depth.shape}")
        return None, None

    return pred_depth, gt_depth


def compute_metrics(pred_depth, gt_depth, min_depth=0.0, max_depth=80.0):
    """Compute validation metrics between predicted and ground truth depth"""
    # Ensure dimensions match
    pred_depth, gt_depth = ensure_same_dimensions(pred_depth, gt_depth)
    if pred_depth is None or gt_depth is None:
        return None

    # Create validity mask
    validity_mask = np.logical_and(gt_depth > min_depth, gt_depth <= max_depth)
    pred_valid = pred_depth[validity_mask]
    gt_valid = gt_depth[validity_mask]

    if len(pred_valid) == 0:
        return None
    
    # Compute metrics (following the same scaling as in sml_main.py)
    mae = eval_utils.mean_abs_err(1000.0 * pred_valid, 1000.0 * gt_valid)
    rmse = eval_utils.root_mean_sq_err(1000.0 * pred_valid, 1000.0 * gt_valid)
    imae = eval_utils.inv_mean_abs_err(0.001 * pred_valid, 0.001 * gt_valid)
    irmse = eval_utils.inv_root_mean_sq_err(0.001 * pred_valid, 0.001 * gt_valid)
    abs_rel = eval_utils.mean_abs_rel_err(1000.0 * pred_valid, 1000.0 * gt_valid)
    sq_rel = eval_utils.mean_sq_rel_err(1000.0 * pred_valid, 1000.0 * gt_valid)
    delta1 = eval_utils.thr_acc(pred_valid, gt_valid)
    
    return {
        'mae': mae,
        'rmse': rmse,
        'imae': imae,
        'irmse': irmse,
        'abs_rel': abs_rel,
        'sq_rel': sq_rel,
        'delta1': delta1,
        'n_valid_pixels': len(pred_valid)
    }


def print_metrics(title, metrics):
    """Print metrics in a formatted way"""
    print(f"\n{title}:")
    print(f"{'='*50}")
    print(f"MAE:      {metrics['mae']:.3f}")
    print(f"RMSE:     {metrics['rmse']:.3f}")
    print(f"iMAE:     {metrics['imae']:.3f}")
    print(f"iRMSE:    {metrics['irmse']:.3f}")
    print(f"Abs_Rel:  {metrics['abs_rel']:.3f}")
    print(f"Sq_Rel:   {metrics['sq_rel']:.3f}")
    print(f"Delta1:   {metrics['delta1']:.3f}")
    print(f"N_Valid:  {metrics['n_valid_pixels']}")


def main():
    # ===== CONFIGURE THESE PATHS =====
    # Path to directory containing ground truth depth PNG files
    gt_dir = "/data/Baidu/ZJU-4DRadarCam/data/gt/"
    
    # Path to directory containing interpolated ground truth depth PNG files  
    gt_interp_dir = "/data/Baidu/ZJU-4DRadarCam/data/gt_interp/"
    
    # Path to directory containing SML output depth PNG files
    sml_output_dir = "/data/Baidu/ZJU-4DRadarCam/result/RadarCam-Depth/sml_depth"
    
    # Depth evaluation range
    min_depth = 0.0
    max_depth = 80.0
    # ==================================
    
    print("SML Depth Estimation Validation Metrics Calculator")
    print("="*55)
    print(f"GT directory: {gt_dir}")
    print(f"GT interpolated directory: {gt_interp_dir}")
    print(f"SML output directory: {sml_output_dir}")
    print(f"Depth range: [{min_depth}, {max_depth}]")
    
    # Check if directories exist
    if not os.path.exists(sml_output_dir):
        print(f"Error: SML output directory '{sml_output_dir}' does not exist!")
        print("Please check the path or run SML inference first.")
        return
    
    # Get all SML output files
    sml_files = sorted(glob.glob(os.path.join(sml_output_dir, '*.png')))
    
    if len(sml_files) == 0:
        print(f"No PNG files found in {sml_output_dir}")
        return
    
    print(f"\nFound {len(sml_files)} SML output files")
    
    # Initialize metric accumulators
    all_metrics_gt = []
    all_metrics_gt_interp = []
    
    # Process each file
    processed_count = 0
    for sml_file in sml_files:
        basename = os.path.basename(sml_file)
        sample_name = os.path.splitext(basename)[0]
        
        # Find corresponding GT files
        gt_file = os.path.join(gt_dir, basename)
        gt_interp_file = os.path.join(gt_interp_dir, basename)
        
        # Load SML depth
        try:
            sml_depth = load_depth_png(sml_file)
        except Exception as e:
            print(f"Error loading SML file {basename}: {e}")
            continue
        
        # Compute metrics against sparse GT (if available)
        if os.path.exists(gt_file):
            try:
                gt_depth = load_depth_png(gt_file)
                metrics_gt = compute_metrics(sml_depth, gt_depth, min_depth, max_depth)
                if metrics_gt:
                    all_metrics_gt.append(metrics_gt)
                    print(f"{sample_name} vs GT: MAE={metrics_gt['mae']:.3f}, RMSE={metrics_gt['rmse']:.3f}")
            except Exception as e:
                print(f"Error processing GT for {basename}: {e}")
        
        # Compute metrics against interpolated GT (if available)
        if os.path.exists(gt_interp_file):
            try:
                gt_interp_depth = load_depth_png(gt_interp_file)
                metrics_gt_interp = compute_metrics(sml_depth, gt_interp_depth, min_depth, max_depth)
                if metrics_gt_interp:
                    all_metrics_gt_interp.append(metrics_gt_interp)
            except Exception as e:
                print(f"Error processing GT interpolated for {basename}: {e}")
        
        processed_count += 1
    
    # Compute and display average metrics
    if all_metrics_gt:
        avg_metrics_gt = {}
        for key in all_metrics_gt[0].keys():
            if key != 'n_valid_pixels':
                avg_metrics_gt[key] = np.mean([m[key] for m in all_metrics_gt])
            else:
                avg_metrics_gt[key] = np.sum([m[key] for m in all_metrics_gt])
        
        print_metrics("Average Metrics vs Sparse GT", avg_metrics_gt)
    else:
        print(f"\nNo valid comparisons against sparse GT found.")
        print(f"Check if GT files exist in: {gt_dir}")
    
    if all_metrics_gt_interp:
        avg_metrics_gt_interp = {}
        for key in all_metrics_gt_interp[0].keys():
            if key != 'n_valid_pixels':
                avg_metrics_gt_interp[key] = np.mean([m[key] for m in all_metrics_gt_interp])
            else:
                avg_metrics_gt_interp[key] = np.sum([m[key] for m in all_metrics_gt_interp])
        
        print_metrics("Average Metrics vs Interpolated GT", avg_metrics_gt_interp)
    else:
        print(f"\nNo valid comparisons against interpolated GT found.")
        print(f"Check if GT interpolated files exist in: {gt_interp_dir}")
    
    print(f"\n{'='*55}")
    print(f"Summary:")
    print(f"- Processed {processed_count} SML output files")
    print(f"- {len(all_metrics_gt)} valid comparisons against sparse GT")
    print(f"- {len(all_metrics_gt_interp)} valid comparisons against interpolated GT")


if __name__ == "__main__":
    main()
