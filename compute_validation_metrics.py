#!/usr/bin/env python3
"""
Compute validation metrics for SML depth estimation results.
This script compares SML output PNG depth maps against ground truth (gt and gt_interp).

Usage:
    python compute_validation_metrics.py --gt_dir <gt_directory> --gt_interp_dir <gt_interp_directory> --sml_output_dir <sml_output_directory> [--output_file <results.txt>]
"""

import os
import argparse
import numpy as np
from PIL import Image
import glob
from pathlib import Path

# Import evaluation utilities
import sys
sys.path.append('.')
import utils.eval_utils as eval_utils


def load_depth_png(depth_path):
    """
    Load depth from PNG file (scaled by 256)
    Args:
        depth_path: path to PNG depth file
    Returns:
        numpy array of depth values
    """
    depth = np.array(Image.open(depth_path), dtype=np.float32) / 256.0
    depth[depth <= 0] = 0.0
    return depth


def create_validity_mask(depth, min_depth=0.0, max_depth=80.0):
    """
    Create validity mask for depth evaluation
    Args:
        depth: depth array
        min_depth: minimum valid depth
        max_depth: maximum valid depth
    Returns:
        boolean mask
    """
    return np.logical_and(depth > min_depth, depth <= max_depth)


def compute_metrics(pred_depth, gt_depth, min_depth=0.0, max_depth=80.0):
    """
    Compute validation metrics between predicted and ground truth depth
    Args:
        pred_depth: predicted depth array
        gt_depth: ground truth depth array
        min_depth: minimum valid depth for evaluation
        max_depth: maximum valid depth for evaluation
    Returns:
        dictionary of metrics
    """
    # Create validity mask
    validity_mask = create_validity_mask(gt_depth, min_depth, max_depth)
    pred_valid = pred_depth[validity_mask]
    gt_valid = gt_depth[validity_mask]
    
    if len(pred_valid) == 0:
        return None
    
    # Compute metrics (following the same scaling as in sml_main.py)
    mae = eval_utils.mean_abs_err(1000.0 * pred_valid, 1000.0 * gt_valid)
    rmse = eval_utils.root_mean_sq_err(1000.0 * pred_valid, 1000.0 * gt_valid)
    imae = eval_utils.inv_mean_abs_err(0.001 * pred_valid, 0.001 * gt_valid)
    irmse = eval_utils.inv_root_mean_sq_err(0.001 * pred_valid, 0.001 * gt_valid)
    abs_rel = eval_utils.mean_abs_rel_err(1000.0 * pred_valid, 1000.0 * gt_valid)
    sq_rel = eval_utils.mean_sq_rel_err(1000.0 * pred_valid, 1000.0 * gt_valid)
    delta1 = eval_utils.thr_acc(pred_valid, gt_valid)
    
    return {
        'mae': mae,
        'rmse': rmse,
        'imae': imae,
        'irmse': irmse,
        'abs_rel': abs_rel,
        'sq_rel': sq_rel,
        'delta1': delta1,
        'n_valid_pixels': len(pred_valid)
    }


def log_results(title, metrics, output_file=None):
    """
    Log evaluation results to console and optionally to file
    """
    message = f"\n{title}:\n"
    message += f"{'Metric':<10} {'Value':<10}\n"
    message += f"{'-'*20}\n"
    message += f"{'MAE':<10} {metrics['mae']:<10.3f}\n"
    message += f"{'RMSE':<10} {metrics['rmse']:<10.3f}\n"
    message += f"{'iMAE':<10} {metrics['imae']:<10.3f}\n"
    message += f"{'iRMSE':<10} {metrics['irmse']:<10.3f}\n"
    message += f"{'Abs_Rel':<10} {metrics['abs_rel']:<10.3f}\n"
    message += f"{'Sq_Rel':<10} {metrics['sq_rel']:<10.3f}\n"
    message += f"{'Delta1':<10} {metrics['delta1']:<10.3f}\n"
    message += f"{'N_Valid':<10} {metrics['n_valid_pixels']:<10}\n"
    
    print(message)
    
    if output_file:
        with open(output_file, 'a') as f:
            f.write(message)


def main():
    parser = argparse.ArgumentParser(description='Compute validation metrics for SML depth estimation')
    parser.add_argument('--gt_dir', required=True, help='Directory containing ground truth depth PNG files')
    parser.add_argument('--gt_interp_dir', required=True, help='Directory containing interpolated ground truth depth PNG files')
    parser.add_argument('--sml_output_dir', required=True, help='Directory containing SML output depth PNG files')
    parser.add_argument('--output_file', default='validation_metrics.txt', help='Output file for results')
    parser.add_argument('--min_depth', type=float, default=0.0, help='Minimum depth for evaluation')
    parser.add_argument('--max_depth', type=float, default=80.0, help='Maximum depth for evaluation')
    
    args = parser.parse_args()
    
    # Get all SML output files
    sml_files = sorted(glob.glob(os.path.join(args.sml_output_dir, '*.png')))
    
    if len(sml_files) == 0:
        print(f"No PNG files found in {args.sml_output_dir}")
        return
    
    print(f"Found {len(sml_files)} SML output files")
    
    # Initialize metric accumulators
    all_metrics_gt = []
    all_metrics_gt_interp = []
    
    # Clear output file
    if os.path.exists(args.output_file):
        os.remove(args.output_file)
    
    # Process each file
    for sml_file in sml_files:
        basename = os.path.basename(sml_file)
        sample_name = os.path.splitext(basename)[0]
        
        # Find corresponding GT files
        gt_file = os.path.join(args.gt_dir, basename)
        gt_interp_file = os.path.join(args.gt_interp_dir, basename)
        
        if not os.path.exists(gt_file):
            print(f"Warning: GT file not found for {basename}")
            continue
            
        if not os.path.exists(gt_interp_file):
            print(f"Warning: GT interpolated file not found for {basename}")
            continue
        
        # Load depth maps
        try:
            sml_depth = load_depth_png(sml_file)
            gt_depth = load_depth_png(gt_file)
            gt_interp_depth = load_depth_png(gt_interp_file)
        except Exception as e:
            print(f"Error loading files for {basename}: {e}")
            continue
        
        # Compute metrics against sparse GT
        metrics_gt = compute_metrics(sml_depth, gt_depth, args.min_depth, args.max_depth)
        if metrics_gt:
            all_metrics_gt.append(metrics_gt)
            print(f"{sample_name} vs GT: MAE={metrics_gt['mae']:.3f}, RMSE={metrics_gt['rmse']:.3f}, Delta1={metrics_gt['delta1']:.3f}")
        
        # Compute metrics against interpolated GT
        metrics_gt_interp = compute_metrics(sml_depth, gt_interp_depth, args.min_depth, args.max_depth)
        if metrics_gt_interp:
            all_metrics_gt_interp.append(metrics_gt_interp)
    
    # Compute average metrics
    if all_metrics_gt:
        avg_metrics_gt = {}
        for key in all_metrics_gt[0].keys():
            if key != 'n_valid_pixels':
                avg_metrics_gt[key] = np.mean([m[key] for m in all_metrics_gt])
            else:
                avg_metrics_gt[key] = np.sum([m[key] for m in all_metrics_gt])
        
        log_results("Average Metrics vs Sparse GT", avg_metrics_gt, args.output_file)
    
    if all_metrics_gt_interp:
        avg_metrics_gt_interp = {}
        for key in all_metrics_gt_interp[0].keys():
            if key != 'n_valid_pixels':
                avg_metrics_gt_interp[key] = np.mean([m[key] for m in all_metrics_gt_interp])
            else:
                avg_metrics_gt_interp[key] = np.sum([m[key] for m in all_metrics_gt_interp])
        
        log_results("Average Metrics vs Interpolated GT", avg_metrics_gt_interp, args.output_file)
    
    print(f"\nResults saved to {args.output_file}")
    print(f"Processed {len(all_metrics_gt)} samples against sparse GT")
    print(f"Processed {len(all_metrics_gt_interp)} samples against interpolated GT")


if __name__ == "__main__":
    main()
