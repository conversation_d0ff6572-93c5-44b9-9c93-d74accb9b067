#!/usr/bin/env python3
"""
Example usage of the validation metrics calculators.
This script demonstrates how to use the metrics calculation tools.
"""

import os
import subprocess
import sys

def run_simple_calculator():
    """Run the simple metrics calculator"""
    print("Running simple metrics calculator...")
    print("="*50)
    
    try:
        result = subprocess.run([sys.executable, "simple_metrics_calculator.py"], 
                              capture_output=True, text=True, check=True)
        print(result.stdout)
        if result.stderr:
            print("Warnings/Errors:")
            print(result.stderr)
    except subprocess.CalledProcessError as e:
        print(f"Error running simple calculator: {e}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)


def run_advanced_calculator():
    """Run the advanced metrics calculator with command line arguments"""
    print("\nRunning advanced metrics calculator...")
    print("="*50)
    
    # Example paths - modify these according to your data structure
    gt_dir = "data/gt"
    gt_interp_dir = "data/gt_interp" 
    sml_output_dir = "results/RadarCam-Depth"
    output_file = "detailed_metrics_results.txt"
    
    cmd = [
        sys.executable, "compute_validation_metrics.py",
        "--gt_dir", gt_dir,
        "--gt_interp_dir", gt_interp_dir,
        "--sml_output_dir", sml_output_dir,
        "--output_file", output_file,
        "--min_depth", "0.0",
        "--max_depth", "80.0"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print(result.stdout)
        if result.stderr:
            print("Warnings/Errors:")
            print(result.stderr)
        
        # Show the output file content if it exists
        if os.path.exists(output_file):
            print(f"\nContent of {output_file}:")
            print("-" * 30)
            with open(output_file, 'r') as f:
                print(f.read())
                
    except subprocess.CalledProcessError as e:
        print(f"Error running advanced calculator: {e}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)


def check_data_structure():
    """Check if the required data structure exists"""
    print("Checking data structure...")
    print("="*30)
    
    required_dirs = [
        "data/gt",
        "data/gt_interp", 
        "results/RadarCam-Depth"
    ]
    
    all_exist = True
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            files = [f for f in os.listdir(dir_path) if f.endswith('.png')]
            print(f"✓ {dir_path} exists ({len(files)} PNG files)")
        else:
            print(f"✗ {dir_path} does not exist")
            all_exist = False
    
    if not all_exist:
        print("\nTo use the metrics calculators, you need:")
        print("1. Ground truth depth images in data/gt/")
        print("2. Interpolated ground truth depth images in data/gt_interp/")
        print("3. SML output depth images in results/RadarCam-Depth/")
        print("\nRun SML inference first to generate the output depth images.")
    
    return all_exist


def main():
    print("Validation Metrics Calculator - Example Usage")
    print("="*55)
    
    # Check data structure first
    data_ready = check_data_structure()
    
    if not data_ready:
        print("\nData structure is not ready. Please prepare the required directories and files.")
        return
    
    print("\nChoose which calculator to run:")
    print("1. Simple calculator (no command line arguments needed)")
    print("2. Advanced calculator (with detailed output file)")
    print("3. Both calculators")
    
    choice = input("\nEnter your choice (1/2/3): ").strip()
    
    if choice == "1":
        run_simple_calculator()
    elif choice == "2":
        run_advanced_calculator()
    elif choice == "3":
        run_simple_calculator()
        run_advanced_calculator()
    else:
        print("Invalid choice. Running simple calculator by default.")
        run_simple_calculator()


if __name__ == "__main__":
    main()
