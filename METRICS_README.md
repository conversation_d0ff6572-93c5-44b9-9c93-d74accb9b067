# Validation Metrics Calculator for SML Depth Estimation

This directory contains tools to compute validation metrics for SML (Scale Map Learner) depth estimation results by comparing the output PNG depth maps against ground truth data.

## Files Created

1. **`compute_validation_metrics.py`** - Advanced metrics calculator with command line interface
2. **`simple_metrics_calculator.py`** - Simple metrics calculator with hardcoded paths
3. **`example_usage.py`** - Example script showing how to use both calculators
4. **`METRICS_README.md`** - This documentation file

## Required Data Structure

Before running the metrics calculators, ensure you have the following directory structure:

```
your_project/
├── data/
│   ├── gt/                    # Sparse ground truth depth PNG files
│   └── gt_interp/             # Interpolated ground truth depth PNG files
├── results/
│   └── RadarCam-Depth/        # SML output depth PNG files
└── utils/
    └── eval_utils.py          # Evaluation utilities (already exists)
```

## Metrics Computed

The calculators compute the following standard depth estimation metrics:

- **MAE** (Mean Absolute Error)
- **RMSE** (Root Mean Square Error) 
- **iMAE** (Inverse Mean Absolute Error)
- **iRMSE** (Inverse Root Mean Square Error)
- **Abs_Rel** (Mean Absolute Relative Error)
- **Sq_Rel** (Mean Squared Relative Error)
- **Delta1** (Threshold Accuracy at 1.25)

## Usage Options

### Option 1: Simple Calculator (Recommended for Quick Use)

```bash
python simple_metrics_calculator.py
```

This script uses hardcoded paths that you can edit at the top of the file:
- `gt_dir = "data/gt"`
- `gt_interp_dir = "data/gt_interp"`
- `sml_output_dir = "results/RadarCam-Depth"`

### Option 2: Advanced Calculator (Command Line Interface)

```bash
python compute_validation_metrics.py \
    --gt_dir data/gt \
    --gt_interp_dir data/gt_interp \
    --sml_output_dir results/RadarCam-Depth \
    --output_file validation_results.txt \
    --min_depth 0.0 \
    --max_depth 80.0
```

**Arguments:**
- `--gt_dir`: Directory containing sparse ground truth depth PNG files
- `--gt_interp_dir`: Directory containing interpolated ground truth depth PNG files  
- `--sml_output_dir`: Directory containing SML output depth PNG files
- `--output_file`: Output file for detailed results (optional)
- `--min_depth`: Minimum depth for evaluation (default: 0.0)
- `--max_depth`: Maximum depth for evaluation (default: 80.0)

### Option 3: Interactive Example

```bash
python example_usage.py
```

This script provides an interactive interface to choose which calculator to run and checks your data structure first.

## Data Format

All depth images should be PNG files where:
- Depth values are stored as 16-bit integers
- Actual depth = pixel_value / 256.0
- Zero or negative values are treated as invalid pixels

## Output

The calculators will output:

1. **Per-sample metrics** (printed to console)
2. **Average metrics** for all processed samples
3. **Comparison against both sparse GT and interpolated GT** (if available)
4. **Detailed results file** (for advanced calculator)

### Example Output

```
Average Metrics vs Sparse GT:
==================================================
MAE:      15.234
RMSE:     28.567
iMAE:     0.012
iRMSE:    0.023
Abs_Rel:  0.156
Sq_Rel:   0.234
Delta1:   0.892
N_Valid:  125000
```

## Notes

- The metrics follow the same scaling and computation as the original SML validation code
- Only pixels with valid ground truth depth (within min_depth and max_depth range) are used for evaluation
- If a corresponding ground truth file is not found, that sample is skipped
- The calculators are designed to work with the existing RadarCam-Depth codebase structure

## Troubleshooting

1. **"No PNG files found"**: Check that your SML output directory contains PNG depth files
2. **"GT file not found"**: Ensure ground truth files have the same names as SML output files
3. **Import errors**: Make sure you're running from the project root directory
4. **Path errors**: Verify that all directory paths are correct and accessible

## Integration with Existing Code

These calculators are designed to work alongside the existing RadarCam-Depth codebase without modifying any existing files. They import the same evaluation utilities (`utils.eval_utils`) used by the original validation code to ensure consistency.
